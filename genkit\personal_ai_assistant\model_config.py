"""
ملف تكوين النماذج المتاحة في Google Vertex AI
Configuration file for available Google Vertex AI models
"""

# النماذج المتاحة في Google Vertex AI
AVAILABLE_MODELS = {
    # نماذج Gemini 2.0 (الأحدث)
    "gemini-2.0-flash": {
        "name": "Gemini 2.0 Flash",
        "description": "أحدث نموذج متعدد الوسائط مع ميزات الجيل التالي وقدرات محسنة",
        "features": [
            "إدخال الصوت والصور والفيديو والنص",
            "إنتاج الكود والصور واستخراج البيانات",
            "زمن استجابة منخفض وأداء محسن",
            "مصمم لتشغيل التجارب الذكية"
        ],
        "status": "متاح",
        "type": "multimodal"
    },
    
    "gemini-2.0-flash-lite": {
        "name": "Gemini 2.0 Flash-Lite",
        "description": "نموذج محسن للكفاءة من ناحية التكلفة وزمن الاستجابة المنخفض",
        "features": [
            "إدخال الصوت والصور والفيديو والنص",
            "يتفوق على 1.5 Flash في معظم المعايير",
            "نافذة سياق مليون رمز مميز",
            "إدخال متعدد الوسائط مثل Flash 2.0"
        ],
        "status": "متاح",
        "type": "multimodal"
    },
    
    # نماذج Gemini 2.5 (معاينة)
    "gemini-2.5-flash": {
        "name": "Gemini 2.5 Flash",
        "description": "معاينة من أحدث إصدار من خط نماذج Flash",
        "features": [
            "إدخال الصوت والصور والفيديو والنص",
            "رؤية عملية التفكير كجزء من الاستجابة",
            "توازن بين السعر والأداء"
        ],
        "status": "معاينة",
        "type": "multimodal"
    },
    
    "gemini-2.5-pro": {
        "name": "Gemini 2.5 Pro",
        "description": "أكثر نماذج التفكير تقدماً حتى الآن",
        "features": [
            "قدرات تفكير متقدمة",
            "أداء فائق في المهام المعقدة",
            "نموذج تفكير متطور"
        ],
        "status": "معاينة",
        "type": "reasoning"
    },
    
    # نماذج Gemini 1.5 (قديمة ولكن مستقرة)
    "gemini-1.5-pro": {
        "name": "Gemini 1.5 Pro",
        "description": "نموذج قوي ومستقر للمهام المعقدة",
        "features": [
            "نافذة سياق كبيرة",
            "أداء ممتاز في التحليل",
            "دعم متعدد الوسائط"
        ],
        "status": "قديم - قد لا يكون متاحاً في المشاريع الجديدة",
        "type": "multimodal"
    },
    
    "gemini-1.5-flash": {
        "name": "Gemini 1.5 Flash",
        "description": "نموذج سريع ومتوازن",
        "features": [
            "سرعة عالية",
            "كفاءة في التكلفة",
            "أداء جيد للمهام العامة"
        ],
        "status": "قديم - قد لا يكون متاحاً في المشاريع الجديدة",
        "type": "multimodal"
    }
}

# النموذج الافتراضي المُوصى به
DEFAULT_MODEL = "gemini-2.0-flash"

# النماذج المُوصى بها حسب نوع المهمة
RECOMMENDED_MODELS = {
    "general": "gemini-2.0-flash",           # للمهام العامة
    "reasoning": "gemini-2.5-pro",          # للتفكير المعقد
    "fast": "gemini-2.0-flash-lite",        # للاستجابة السريعة
    "preview": "gemini-2.5-flash",          # لتجربة الميزات الجديدة
    "cost_effective": "gemini-2.0-flash-lite"  # للكفاءة في التكلفة
}

def get_model_info(model_name: str) -> dict:
    """
    الحصول على معلومات نموذج معين
    Get information about a specific model
    """
    return AVAILABLE_MODELS.get(model_name, {
        "name": "نموذج غير معروف",
        "description": "النموذج المطلوب غير موجود في القائمة",
        "status": "غير متاح",
        "features": []
    })

def get_available_models() -> list:
    """
    الحصول على قائمة بجميع النماذج المتاحة
    Get list of all available models
    """
    return list(AVAILABLE_MODELS.keys())

def get_recommended_model(task_type: str = "general") -> str:
    """
    الحصول على النموذج المُوصى به لنوع مهمة معين
    Get recommended model for a specific task type
    """
    return RECOMMENDED_MODELS.get(task_type, DEFAULT_MODEL)

def is_model_available(model_name: str) -> bool:
    """
    التحقق من توفر نموذج معين
    Check if a model is available
    """
    model_info = AVAILABLE_MODELS.get(model_name, {})
    return model_info.get("status", "").lower() == "متاح"
