from typing import List, Dict, Any
import os

# استيراد pandas بشكل آمن
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False


class ExcelTools:
    """أدوات للتعامل مع ملفات إكسل"""

    @staticmethod
    def read_excel(file_path: str, sheet_name: str = None) -> Dict[str, Any]:
        """
        قراءة ملف إكسل وإرجاع البيانات

        Args:
            file_path: مسار ملف الإكسل
            sheet_name: اسم الورقة (اختياري)

        Returns:
            قاموس يحتوي على البيانات
        """
        if not PANDAS_AVAILABLE:
            return {"error": "pandas غير مثبت. يرجى تثبيته باستخدام: pip install pandas openpyxl"}

        try:
            if not os.path.exists(file_path):
                return {"error": f"الملف غير موجود: {file_path}"}

            if sheet_name:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            else:
                excel_file = pd.ExcelFile(file_path)
                df = {sheet: pd.read_excel(file_path, sheet_name=sheet) for sheet in excel_file.sheet_names}

            return {"data": df.to_dict(orient='records') if not isinstance(df, dict) else
                    {k: v.to_dict(orient='records') for k, v in df.items()}}

        except Exception as e:
            return {"error": f"خطأ في قراءة ملف الإكسل: {str(e)}"}

    @staticmethod
    def write_excel(data: List[Dict], file_path: str, sheet_name: str = "Sheet1"):
        """
        كتابة البيانات إلى ملف إكسل

        Args:
            data: البيانات المراد كتابتها (قائمة من القواميس)
            file_path: مسار ملف الإخراج
            sheet_name: اسم الورقة (اختياري)
        """
        if not PANDAS_AVAILABLE:
            return {"error": "pandas غير مثبت. يرجى تثبيته باستخدام: pip install pandas openpyxl"}

        try:
            df = pd.DataFrame(data)
            df.to_excel(file_path, sheet_name=sheet_name, index=False)
            return {"success": True, "message": f"تم حفظ البيانات بنجاح في {file_path}"}
        except Exception as e:
            return {"error": f"خطأ في كتابة ملف الإكسل: {str(e)}"}

    @staticmethod
    def analyze_data(data: List[Dict]) -> Dict[str, Any]:
        """
        تحليل البيانات الأساسية

        Args:
            data: البيانات المراد تحليلها

        Returns:
            نتائج التحليل
        """
        if not PANDAS_AVAILABLE:
            return {"error": "pandas غير مثبت. يرجى تثبيته باستخدام: pip install pandas openpyxl"}

        try:
            if not data:
                return {"error": "لا توجد بيانات للتحليل"}

            df = pd.DataFrame(data)
            analysis = {
                "row_count": len(df),
                "columns": list(df.columns),
                "data_types": df.dtypes.astype(str).to_dict(),
                "missing_values": df.isnull().sum().to_dict(),
                "basic_stats": df.describe().to_dict() if not df.empty else {}
            }

            return {"analysis": analysis}

        except Exception as e:
            return {"error": f"خطأ في تحليل البيانات: {str(e)}"}
