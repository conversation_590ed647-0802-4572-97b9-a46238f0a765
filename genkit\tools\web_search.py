from typing import Dict


class WebSearch:
    """أدوات للبحث على الإنترنت (نسخة مبسطة)"""

    @staticmethod
    def search_google(query: str, num_results: int = 5) -> Dict:
        """البحث على جوجل"""
        return {
            "error": "وظيفة البحث غير متاحة حالياً. يرجى تثبيت requests و beautifulsoup4"
        }

    @staticmethod
    def get_webpage_content(url: str) -> Dict:
        """جلب محتوى صفحة ويب"""
        return {
            "error": "وظيفة جلب المحتوى غير متاحة حالياً. يرجى تثبيت requests و beautifulsoup4"
        }
