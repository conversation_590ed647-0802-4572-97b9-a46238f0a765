# 🤖 المساعد الشخصي الذكي - Personal AI Assistant
## مشروع متكامل مع أحدث نماذج Google Vertex AI

مساعد شخصي ذكي متطور مبني باستخدام **Google Agent Development Kit (ADK)** وأحدث نماذج **Vertex AI** مع دعم **Gemini 2.0 Flash** و **Gemini 2.5 Pro**.

---

## 🆕 آخر التحديثات (ديسمبر 2024)

### 🔥 الميزات الجديدة:
- ✅ **دعم Gemini 2.0 Flash** - أحدث نموذج متعدد الوسائط
- ✅ **دعم Gemini 2.5 Pro** - أكثر نماذج التفكير تقدماً
- ✅ **تفعيل جميع الأدوات المتقدمة** (Excel, Web Search, File Operations)
- ✅ **مثبت التبعيات الآلي** للتثبيت السريع
- ✅ **أداة اختبار النماذج** للتحقق من التوفر
- ✅ **نظام إدارة النماذج الذكي** مع التوصيات

### 🛠️ التحسينات:
- ✅ **معالجة أخطاء محسنة** مع رسائل واضحة
- ✅ **دعم أفضل للغة العربية** في جميع الوظائف
- ✅ **هيكل مشروع منظم** مع توثيق شامل
- ✅ **أدوات تطوير متقدمة** للتخصيص والتوسع

---

## 🚀 البدء السريع

### 1. **التثبيت الآلي (الطريقة المُوصى بها):**
```bash
# تثبيت جميع التبعيات تلقائياً
python install_dependencies.py
```

### 2. **إعداد Google Cloud:**
```bash
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/credentials.json"
```

### 3. **تشغيل المساعد:**
```bash
cd personal_ai_assistant
adk web --port 8000
```

### 4. **الوصول للواجهة:**
افتح المتصفح وانتقل إلى: `http://localhost:8000`

---

## 🤖 النماذج المدعومة

### ✅ النماذج المتاحة:
- **Gemini 2.0 Flash** - أحدث نموذج متعدد الوسائط (افتراضي)
- **Gemini 2.0 Flash-Lite** - محسن للكفاءة والسرعة

### 🔬 النماذج التجريبية:
- **Gemini 2.5 Pro** - أكثر نماذج التفكير تقدماً
- **Gemini 2.5 Flash** - معاينة أحدث إصدار Flash

### 🧪 اختبار النماذج:
```bash
# اختبار النماذج المتاحة في مشروعك
python test_models.py
```

---

## ✨ الوظائف المتاحة

| الوظيفة | الوصف | الحالة |
|---------|--------|--------|
| 🌤️ **معلومات الطقس** | تقارير الطقس للمدن العالمية | ✅ متاح |
| 🕐 **معلومات التوقيت** | الوقت الحالي في أي مدينة | ✅ متاح |
| 🔍 **البحث والمعرفة** | قاعدة معرفة تقنية محدثة | ✅ محدث |
| 📝 **إدارة المهام** | إضافة وعرض المهام | ✅ متاح |
| 📊 **أدوات Excel** | قراءة وكتابة ملفات Excel | ✅ مفعل |
| 🌐 **البحث على الإنترنت** | بحث Google وجلب المحتوى | ✅ مفعل |
| 📂 **إدارة الملفات** | قراءة وكتابة الملفات | ✅ مفعل |

---

## 📁 هيكل المشروع

```
genkit/
├── 📄 README.md                    # هذا الملف
├── 🔧 install_dependencies.py      # مثبت التبعيات الآلي
├── 📦 requirements.txt             # قائمة التبعيات
├── 🧪 test_models.py               # أداة اختبار النماذج
└── personal_ai_assistant/          # المساعد الرئيسي
    ├── 🤖 agent.py                 # الكود الرئيسي
    ├── 🔧 model_config.py          # تكوين النماذج
    ├── 📄 README.md                # توثيق مفصل
    └── 🗂️ tools/                   # الأدوات المتقدمة
        ├── 📊 excel_tools.py       # أدوات Excel
        ├── 🌐 web_search.py        # أدوات البحث
        └── 📂 file_operations.py   # أدوات الملفات
```

---

## 🛠️ المتطلبات

### **النظام:**
- Python 3.8+
- 4GB RAM (موصى به 8GB)
- اتصال إنترنت مستقر

### **Google Cloud:**
- حساب Google Cloud نشط
- مشروع مع Vertex AI API مفعل
- إعداد الفوترة (يمكن استخدام الرصيد المجاني)

---

## 🎯 أمثلة الاستخدام

### **أسئلة عامة:**
- "مرحباً، ما هي قدراتك الجديدة؟"
- "ما هو الطقس في الرياض؟"
- "كم الساعة في نيويورك؟"

### **البحث والمعلومات:**
- "ابحث لي عن معلومات حول Gemini 2.0"
- "ما هو الذكاء الاصطناعي؟"
- "ابحث على الإنترنت عن أحدث أخبار التكنولوجيا"

### **إدارة الملفات:**
- "اقرأ ملف Excel من المسار C:\data\sales.xlsx"
- "أنشئ ملف Excel بقائمة مهامي"
- "اعرض محتويات مجلد المستندات"

---

## 🔗 روابط مفيدة

- [توثيق Google ADK](https://google.github.io/adk-docs/)
- [توثيق Vertex AI](https://cloud.google.com/vertex-ai/docs)
- [نماذج Google Vertex AI](https://cloud.google.com/vertex-ai/generative-ai/docs/models)
- [مجتمع Google Cloud](https://www.googlecloudcommunity.com/)

---

## 🆘 الدعم والمساعدة

### **استكشاف الأخطاء:**
1. تأكد من تثبيت جميع التبعيات: `python install_dependencies.py`
2. اختبر النماذج المتاحة: `python test_models.py`
3. تحقق من إعدادات Google Cloud

### **الحصول على المساعدة:**
- راجع التوثيق المفصل في `personal_ai_assistant/README.md`
- تحقق من ملفات الأدوات في مجلد `tools/`
- استخدم أداة اختبار النماذج للتشخيص

---

**🎉 مساعد ذكي متطور جاهز للاستخدام مع أحدث تقنيات Google!**
