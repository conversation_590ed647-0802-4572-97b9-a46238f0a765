#!/usr/bin/env python3
"""
تشغيل المساعد الشخصي الذكي
Run Personal AI Assistant
"""

import subprocess
import sys
import os

def check_dependencies():
    """التحقق من التبعيات المطلوبة"""
    try:
        import requests
        import json
        import pandas
        import numpy
        from bs4 import BeautifulSoup
        from google.adk.agents import Agent
        print("✅ جميع التبعيات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ تبعية مفقودة: {e}")
        print("يرجى تشغيل: python install_dependencies.py")
        return False

def check_env_file():
    """التحقق من ملف .env"""
    if os.path.exists('.env'):
        print("✅ ملف .env موجود")
        return True
    else:
        print("❌ ملف .env غير موجود")
        print("يرجى التأكد من وجود ملف .env مع إعدادات Google Cloud")
        return False

def run_assistant():
    """تشغيل المساعد"""
    print("🚀 بدء تشغيل المساعد الشخصي الذكي...")

    # التحقق من التبعيات
    if not check_dependencies():
        return False

    # التحقق من ملف .env
    if not check_env_file():
        return False

    # تشغيل ADK
    try:
        print("🌐 تشغيل واجهة الويب...")
        print("📱 ستفتح الواجهة على: http://localhost:8000")
        print("🔄 للإيقاف اضغط Ctrl+C")

        # الانتقال للمجلد الأب
        parent_dir = os.path.dirname(os.getcwd())
        os.chdir(parent_dir)

        # تشغيل adk web
        subprocess.run(["adk", "web"], check=True)

    except subprocess.CalledProcessError:
        print("❌ فشل في تشغيل ADK")
        return False
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف المساعد بنجاح")
        return True

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🤖 مساعد شخصي ذكي - Personal AI Assistant")
    print("=" * 50)

    run_assistant()

if __name__ == "__main__":
    main()
