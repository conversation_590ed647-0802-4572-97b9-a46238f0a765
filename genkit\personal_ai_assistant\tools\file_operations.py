from typing import Dict, List
import os
from pathlib import Path


class FileOperations:
    """أدوات للتعامل مع الملفات والمجلدات"""

    @staticmethod
    def read_file(file_path: str) -> Dict:
        """
        قراءة ملف نصي

        Args:
            file_path: مسار الملف

        Returns:
            محتوى الملف أو رسالة خطأ
        """
        try:
            if not os.path.exists(file_path):
                return {"error": f"الملف غير موجود: {file_path}"}

            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            return {
                "content": content,
                "size": len(content),
                "lines": len(content.splitlines())
            }

        except Exception as e:
            return {"error": f"خطأ في قراءة الملف: {str(e)}"}

    @staticmethod
    def write_file(file_path: str, content: str) -> Dict:
        """
        كتابة ملف نصي

        Args:
            file_path: مسار الملف
            content: المحتوى المراد كتابته

        Returns:
            حالة العملية
        """
        try:
            # إنشاء المجلد إذا لم يكن موجود
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)

            return {
                "success": True,
                "message": f"تم حفظ الملف بنجاح: {file_path}",
                "size": len(content)
            }

        except Exception as e:
            return {"error": f"خطأ في كتابة الملف: {str(e)}"}

    @staticmethod
    def list_directory(directory_path: str) -> Dict:
        """
        عرض محتويات مجلد

        Args:
            directory_path: مسار المجلد

        Returns:
            قائمة بمحتويات المجلد
        """
        try:
            if not os.path.exists(directory_path):
                return {"error": f"المجلد غير موجود: {directory_path}"}

            if not os.path.isdir(directory_path):
                return {"error": f"المسار ليس مجلد: {directory_path}"}

            items = []
            for item in os.listdir(directory_path):
                item_path = os.path.join(directory_path, item)
                is_dir = os.path.isdir(item_path)
                size = None if is_dir else os.path.getsize(item_path)

                items.append({
                    "name": item,
                    "is_dir": is_dir,
                    "size": size
                })

            return {"items": items}

        except Exception as e:
            return {"error": f"خطأ في قراءة المجلد: {str(e)}"}

    @staticmethod
    def create_directory(directory_path: str) -> Dict:
        """
        إنشاء مجلد جديد

        Args:
            directory_path: مسار المجلد الجديد

        Returns:
            حالة العملية
        """
        try:
            os.makedirs(directory_path, exist_ok=True)
            return {
                "success": True,
                "message": f"تم إنشاء المجلد بنجاح: {directory_path}"
            }

        except Exception as e:
            return {"error": f"خطأ في إنشاء المجلد: {str(e)}"}
