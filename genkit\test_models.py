#!/usr/bin/env python3
"""
اختبار النماذج المختلفة المتاحة في Google Vertex AI
Test different available models in Google Vertex AI
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'personal_ai_assistant'))

from model_config import AVAILABLE_MODELS, get_model_info, get_recommended_model

def test_model_availability():
    """اختبار توفر النماذج المختلفة"""
    print("🔍 اختبار النماذج المتاحة في Google Vertex AI")
    print("=" * 60)
    
    # النماذج المُوصى بها للاختبار
    models_to_test = [
        "gemini-2.0-flash",
        "gemini-2.0-flash-lite", 
        "gemini-2.5-flash",
        "gemini-2.5-pro",
        "gemini-1.5-flash",
        "gemini-1.5-pro"
    ]
    
    for model in models_to_test:
        info = get_model_info(model)
        print(f"\n📋 النموذج: {model}")
        print(f"   الاسم: {info.get('name', 'غير معروف')}")
        print(f"   الحالة: {info.get('status', 'غير معروف')}")
        print(f"   النوع: {info.get('type', 'غير معروف')}")
        print(f"   الوصف: {info.get('description', 'غير متوفر')}")
        
        if info.get('features'):
            print("   الميزات:")
            for feature in info['features']:
                print(f"     • {feature}")

def show_recommendations():
    """عرض التوصيات للنماذج"""
    print("\n\n🎯 التوصيات للنماذج حسب نوع المهمة")
    print("=" * 60)
    
    recommendations = {
        "general": "للمهام العامة",
        "reasoning": "للتفكير المعقد", 
        "fast": "للاستجابة السريعة",
        "preview": "لتجربة الميزات الجديدة",
        "cost_effective": "للكفاءة في التكلفة"
    }
    
    for task_type, description in recommendations.items():
        recommended = get_recommended_model(task_type)
        print(f"\n📌 {description}: {recommended}")
        model_info = get_model_info(recommended)
        print(f"   الحالة: {model_info.get('status', 'غير معروف')}")

def main():
    """الدالة الرئيسية"""
    print("🚀 مرحباً بك في اختبار نماذج Google Vertex AI")
    print("=" * 60)
    
    test_model_availability()
    show_recommendations()
    
    print("\n\n💡 ملاحظات مهمة:")
    print("• النماذج 1.5 قد لا تكون متاحة في المشاريع الجديدة")
    print("• النماذج 2.0 هي الأحدث والأكثر استقراراً")
    print("• النماذج 2.5 في مرحلة المعاينة")
    print("• تأكد من تفعيل النماذج في مشروعك على Google Cloud")
    
    print("\n🔗 للمزيد من المعلومات:")
    print("https://cloud.google.com/vertex-ai/generative-ai/docs/models")

if __name__ == "__main__":
    main()
