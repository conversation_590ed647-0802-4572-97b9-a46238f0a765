"""
مساعد احتياطي لمجلد الأدوات
Fallback agent for tools directory
"""

from google.adk.agents import Agent
import datetime
from zoneinfo import ZoneInfo

# تعريف الوظائف الأساسية محلياً
def get_weather(city: str) -> dict:
    """وظيفة الطقس المحلية"""
    weather_data = {
        "cairo": "الطقس في القاهرة مشمس مع درجة حرارة 28 مئوية",
        "riyadh": "الطقس في الرياض حار مع درجة حرارة 35 مئوية",
        "dubai": "الطقس في دبي مشمس مع درجة حرارة 32 مئوية",
        "new york": "The weather in New York is sunny with a temperature of 25°C",
        "london": "The weather in London is cloudy with a temperature of 18°C"
    }

    city_lower = city.lower()
    if city_lower in weather_data:
        return {
            "status": "success",
            "report": weather_data[city_lower]
        }
    else:
        return {
            "status": "error",
            "error_message": f"معذرة، لا تتوفر معلومات الطقس لمدينة '{city}'"
        }

def get_current_time(city: str) -> dict:
    """وظيفة الوقت المحلية"""
    timezone_map = {
        "cairo": "Africa/Cairo",
        "riyadh": "Asia/Riyadh",
        "dubai": "Asia/Dubai",
        "new york": "America/New_York",
        "london": "Europe/London",
        "tokyo": "Asia/Tokyo",
        "paris": "Europe/Paris"
    }

    city_lower = city.lower()
    if city_lower in timezone_map:
        try:
            tz_identifier = timezone_map[city_lower]
            tz = ZoneInfo(tz_identifier)
            now = datetime.datetime.now(tz)
            return {
                "status": "success",
                "report": f"الوقت الحالي في {city} هو {now.strftime('%Y-%m-%d %H:%M:%S %Z')}"
            }
        except Exception as e:
            return {
                "status": "error",
                "error_message": f"خطأ في الحصول على الوقت: {str(e)}"
            }
    else:
        return {
            "status": "error",
            "error_message": f"معذرة، لا تتوفر معلومات التوقيت لمدينة '{city}'"
        }

def redirect_to_main() -> dict:
    """توجيه للمساعد الرئيسي"""
    return {
        "status": "info",
        "report": """🔄 أنت تستخدم المساعد من مجلد 'tools'

للحصول على المساعد الكامل مع جميع الميزات:
1. اذهب للصفحة الرئيسية: http://localhost:8000
2. اختر 'personal_ai_assistant' بدلاً من 'tools'

يمكنني مساعدتك حالياً في:
- الطقس: "ما الطقس في القاهرة؟"
- الوقت: "كم الساعة في نيويورك؟"

للميزات المتقدمة (البحث والمهام)، استخدم المساعد الرئيسي."""
    }

# إنشاء المساعد مع الوظائف المحلية
root_agent = Agent(
    name="personal_ai_assistant_tools",
    model="gemini-1.5-pro",
    description="مساعد شخصي ذكي (نسخة مجلد الأدوات)",
    instruction="""أنا مساعد شخصي ذكي (نسخة مجلد الأدوات).

🌤️ يمكنني مساعدتك في معرفة الطقس
🕐 يمكنني مساعدتك في معرفة الوقت

⚠️ ملاحظة مهمة: أنت تستخدم المساعد من مجلد 'tools'
للحصول على جميع الميزات (البحث والمهام)، اختر 'personal_ai_assistant' من الصفحة الرئيسية.

كيف يمكنني مساعدتك؟""",
    tools=[get_weather, get_current_time, redirect_to_main]
)

# تأكيد أن المساعد تم إنشاؤه بنجاح
# print(f"✅ تم إنشاء المساعد بنجاح: {root_agent.name}")
