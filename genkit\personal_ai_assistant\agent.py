"""
مساعد شخصي ذكي متعدد المهارات باستخدام Google ADK
Personal AI Assistant using Google Agent Development Kit
"""

from zoneinfo import ZoneInfo
from google.adk.agents import Agent
import datetime
import requests
import json
import os
from typing import Dict, List, Any, Optional

# استيراد الأدوات من مجلد tools
try:
    from tools import ExcelTools, WebSearch, FileOperations
except ImportError:
    # في حالة عدم توفر الأدوات، استخدم بدائل بسيطة
    ExcelTools = None
    WebSearch = None
    FileOperations = None

def get_weather(city: str) -> dict:
    """
    يحصل على تقرير الطقس الحالي لمدينة محددة
    Retrieves the current weather report for a specified city.

    Args:
        city (str): اسم المدينة

    Returns:
        dict: تقرير الطقس مع حالة النجاح أو الخطأ
    """
    # مثال بسيط - يمكن تطويره لاحقً للاتصال بـ API حقيقي
    weather_data = {
        "cairo": "الطقس في القاهرة مشمس مع درجة حرارة 28 مئوية",
        "riyadh": "الطقس في الرياض حار مع درجة حرارة 35 مئوية",
        "dubai": "الطقس في دبي مشمس مع درجة حرارة 32 مئوية",
        "new york": "The weather in New York is sunny with a temperature of 25°C",
        "london": "The weather in London is cloudy with a temperature of 18°C"
    }

    city_lower = city.lower()
    if city_lower in weather_data:
        return {
            "status": "success",
            "report": weather_data[city_lower]
        }
    else:
        return {
            "status": "error",
            "error_message": f"معذرة، لا تتوفر معلومات الطقس لمدينة '{city}'"
        }

def get_current_time(city: str) -> dict:
    """
    يعرض الوقت الحالي في مدينة محددة
    Returns the current time in a specified city.

    Args:
        city (str): اسم المدينة

    Returns:
        dict: الوقت الحالي مع حالة النجاح أو الخطأ
    """
    # خريطة المدن والمناطق الزمنية
    timezone_map = {
        "cairo": "Africa/Cairo",
        "riyadh": "Asia/Riyadh",
        "dubai": "Asia/Dubai",
        "new york": "America/New_York",
        "london": "Europe/London",
        "tokyo": "Asia/Tokyo",
        "paris": "Europe/Paris"
    }

    city_lower = city.lower()
    if city_lower in timezone_map:
        try:
            tz_identifier = timezone_map[city_lower]
            tz = ZoneInfo(tz_identifier)
            now = datetime.datetime.now(tz)

            return {
                "status": "success",
                "report": f"الوقت الحالي في {city} هو {now.strftime('%Y-%m-%d %H:%M:%S %Z')}"
            }
        except Exception as e:
            return {
                "status": "error",
                "error_message": f"خطأ في الحصول على الوقت: {str(e)}"
            }
    else:
        return {
            "status": "error",
            "error_message": f"معذرة، لا تتوفر معلومات التوقيت لمدينة '{city}'"
        }

def search_information(query: str) -> dict:
    """
    يبحث عن المعلومات ويقدم إجابات مفيدة
    Searches for information and provides helpful answers.

    Args:
        query (str): استعلام البحث

    Returns:
        dict: نتائج البحث أو معلومات مفيدة
    """
    # قاعدة معرفة بسيطة - يمكن توسيعها لاحقً
    knowledge_base = {
        "python": "Python هي لغة برمجة قوية ومرنة، مناسبة للذكاء الاصطناعي وتطوير الويب",
        "ai": "الذكاء الاصطناعي هو تقنية تمكن الآلات من محاكاة الذكاء البشري",
        "google cloud": "Google Cloud هي منصة حوسبة سحابية توفر خدمات متنوعة للذكاء الاصطناعي",
        "vertex ai": "Vertex AI هي منصة Google للذكاء الاصطناعي التوليدي والتعلم الآلي"
    }

    query_lower = query.lower()
    for key, value in knowledge_base.items():
        if key in query_lower:
            return {
                "status": "success",
                "report": value
            }

    return {
        "status": "partial",
        "report": f"لم أجد معلومات محددة عن '{query}'. يمكنني مساعدتك في الطقس، الوقت، أو معلومات تقنية أساسية."
    }

def manage_tasks(action: str, task: str = "") -> dict:
    """
    يدير المهام والتذكيرات
    Manages tasks and reminders.

    Args:
        action (str): العمل المطلوب (add, list, complete)
        task (str): وصف المهمة

    Returns:
        dict: حالة العملية
    """
    # نظام مهام بسيط - يمكن تطويره لاحقً
    if action == "add" and task:
        return {
            "status": "success",
            "report": f"تم إضافة المهمة: {task}"
        }
    elif action == "list":
        return {
            "status": "success",
            "report": "قائمة المهام فارغة حالياً. يمكنك إضافة مهام جديدة."
        }
    else:
        return {
            "status": "error",
            "error_message": "يرجى تحديد عمل صحيح (add, list) ووصف المهمة إذا لزم الأمر"
        }

def read_excel_file(file_path: str, sheet_name: str = None) -> dict:
    """
    يقرأ ملف إكسل ويعرض محتواه
    Reads an Excel file and returns its content.

    Args:
        file_path (str): مسار ملف الإكسل
        sheet_name (str, optional): اسم الورقة المطلوبة. Defaults to None.

    Returns:
        dict: محتوى ملف الإكسل أو رسالة الخطأ
    """
    if ExcelTools is None:
        return {
            "status": "error",
            "error_message": "أدوات Excel غير متاحة حالياً. يرجى التأكد من تثبيت التبعيات المطلوبة."
        }

    try:
        result = ExcelTools.read_excel(file_path, sheet_name)
        if "error" in result:
            return {
                "status": "error",
                "error_message": result["error"]
            }
        return {
            "status": "success",
            "report": "تم قراءة ملف الإكسل بنجاح",
            "data": result.get("data", {})
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": f"حدث خطأ أثناء قراءة ملف الإكسل: {str(e)}"
        }

def write_to_excel(data: List[Dict], file_path: str, sheet_name: str = "Sheet1") -> dict:
    """
    يكتب البيانات إلى ملف إكسل
    Writes data to an Excel file.

    Args:
        data (List[Dict]): البيانات المراد كتابتها
        file_path (str): مسار ملف الإخراج
        sheet_name (str, optional): اسم الورقة. Defaults to "Sheet1".

    Returns:
        dict: حالة العملية
    """
    if ExcelTools is None:
        return {
            "status": "error",
            "error_message": "أدوات Excel غير متاحة حالياً. يرجى التأكد من تثبيت التبعيات المطلوبة."
        }

    try:
        result = ExcelTools.write_excel(data, file_path, sheet_name)
        if "error" in result:
            return {
                "status": "error",
                "error_message": result["error"]
            }
        return {
            "status": "success",
            "report": result.get("message", "تم حفظ البيانات في ملف الإكسل بنجاح")
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": f"حدث خطأ أثناء كتابة ملف الإكسل: {str(e)}"
        }

def search_web(query: str, num_results: int = 3) -> dict:
    """
    يبحث على الإنترنت عن استعلام معين
    Searches the web for a specific query.

    Args:
        query (str): استعلام البحث
        num_results (int, optional): عدد النتائج المطلوبة. Defaults to 3.

    Returns:
        dict: نتائج البحث أو رسالة الخطأ
    """
    if WebSearch is None:
        return {
            "status": "error",
            "error_message": "أدوات البحث غير متاحة حالياً. يرجى التأكد من تثبيت التبعيات المطلوبة."
        }

    try:
        result = WebSearch.search_google(query, num_results)
        if "error" in result:
            return {
                "status": "error",
                "error_message": result["error"]
            }
        return {
            "status": "success",
            "report": f"تم العثور على {len(result.get('results', []))} نتيجة",
            "results": result.get("results", [])
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": f"حدث خطأ أثناء البحث على الإنترنت: {str(e)}"
        }

def read_file_content(file_path: str) -> dict:
    """
    يقرأ محتوى ملف نصي
    Reads the content of a text file.

    Args:
        file_path (str): مسار الملف

    Returns:
        dict: محتوى الملف أو رسالة الخطأ
    """
    if FileOperations is None:
        return {
            "status": "error",
            "error_message": "أدوات الملفات غير متاحة حالياً. يرجى التأكد من تثبيت التبعيات المطلوبة."
        }

    try:
        result = FileOperations.read_file(file_path)
        if "error" in result:
            return {
                "status": "error",
                "error_message": result["error"]
            }
        return {
            "status": "success",
            "report": "تم قراءة الملف بنجاح",
            "content": result.get("content", "")
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": f"حدث خطأ أثناء قراءة الملف: {str(e)}"
        }

def list_directory_contents(directory_path: str) -> dict:
    """
    يعرض محتويات مجلد
    Lists the contents of a directory.

    Args:
        directory_path (str): مسار المجلد

    Returns:
        dict: محتويات المجلد أو رسالة الخطأ
    """
    if FileOperations is None:
        return {
            "status": "error",
            "error_message": "أدوات الملفات غير متاحة حالياً. يرجى التأكد من تثبيت التبعيات المطلوبة."
        }

    try:
        result = FileOperations.list_directory(directory_path)
        if "error" in result:
            return {
                "status": "error",
                "error_message": result["error"]
            }

        items = result.get("items", [])
        if not items:
            return {
                "status": "success",
                "report": "المجلد فارغ",
                "items": []
            }

        formatted_items = []
        for item in items:
            item_type = "مجلد" if item.get("is_dir") else "ملف"
            size = f"{item.get('size')} بايت" if item.get("size") is not None else "-"
            formatted_items.append({
                "الاسم": item.get("name", ""),
                "النوع": item_type,
                "الحجم": size
            })

        return {
            "status": "success",
            "report": f"تم العثور على {len(items)} عنصر في المجلد",
            "items": formatted_items
        }

    except Exception as e:
        return {
            "status": "error",
            "error_message": f"حدث خطأ أثناء قراءة المجلد: {str(e)}"
        }

# إنشاء المساعد الشخصي الذكي
root_agent = Agent(
    name="personal_ai_assistant",
    model="gemini-1.5-pro",  # تغيير النموذج لدعم Live API
    description="مساعد شخصي ذكي متعدد المهارات يمكنه مساعدتك في مختلف المجالات",
    instruction="""أنا مساعدك الشخصي الذكي. يمكنني مساعدتك في:

🌤️ معرفة الطقس في مختلف المدن
🕐 معرفة الوقت الحالي في أي مدينة
🔍 البحث عن المعلومات والإجابة على الأسئلة
📝 إدارة المهام والتذكيرات
📊 التعامل مع ملفات إكسل (قراءة وكتابة)
🌐 البحث على الإنترنت
📂 إدارة الملفات والمجلدات

أتحدث باللغة العربية والإنجليزية، وأسعى لتقديم إجابات مفيدة ودقيقة.
كيف يمكنني مساعدتك اليوم؟""",
    tools=[
        get_weather,
        get_current_time,
        search_information,
        manage_tasks,
        read_excel_file,
        write_to_excel,
        search_web,
        read_file_content,
        list_directory_contents
    ]
)

