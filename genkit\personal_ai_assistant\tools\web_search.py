from typing import Dict, List
from urllib.parse import quote_plus

# استيراد المكتبات بشكل آمن
try:
    import requests
    from bs4 import BeautifulSoup
    WEB_TOOLS_AVAILABLE = True
except ImportError:
    WEB_TOOLS_AVAILABLE = False


class WebSearch:
    """أدوات للبحث على الإنترنت (نسخة مبسطة)"""

    @staticmethod
    def search_google(query: str, num_results: int = 5) -> Dict:
        """
        البحث على جوجل

        Args:
            query: النص المراد البحث عنه
            num_results: عدد النتائج المطلوبة

        Returns:
            قائمة بالنتائج
        """
        if not WEB_TOOLS_AVAILABLE:
            return {"error": "أدوات البحث غير متاحة. يرجى تثبيت requests و beautifulsoup4"}

        try:
            # هذه وظيفة بسيطة، في التطبيق الحقيقي يمكن استخدام واجهة برمجة تطبيقات جوجل
            url = f"https://www.google.com/search?q={quote_plus(query)}&num={num_results}"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers)
            soup = BeautifulSoup(response.text, 'html.parser')

            results = []
            for g in soup.find_all('div', class_='tF2Cxc'):
                title_elem = g.find('h3')
                link_elem = g.find('a')
                snippet_elem = g.find('span', class_='aCOpRe')

                if title_elem and link_elem:
                    title = title_elem.get_text()
                    link = link_elem.get('href')
                    snippet = snippet_elem.get_text() if snippet_elem else ""

                    results.append({
                        'title': title,
                        'link': link,
                        'snippet': snippet
                    })

                    if len(results) >= num_results:
                        break

            return {"results": results}

        except Exception as e:
            return {"error": f"خطأ في البحث: {str(e)}"}

    @staticmethod
    def get_webpage_content(url: str) -> Dict:
        """
        جلب محتوى صفحة ويب

        Args:
            url: رابط الصفحة

        Returns:
            محتوى الصفحة
        """
        if not WEB_TOOLS_AVAILABLE:
            return {"error": "أدوات البحث غير متاحة. يرجى تثبيت requests و beautifulsoup4"}

        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers)
            soup = BeautifulSoup(response.text, 'html.parser')

            # إزالة العناصر غير المرغوب فيها
            for script in soup(["script", "style"]):
                script.decompose()

            text = soup.get_text()
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)

            return {
                "content": text[:2000],  # أول 2000 حرف
                "title": soup.title.string if soup.title else "بدون عنوان"
            }

        except Exception as e:
            return {"error": f"خطأ في جلب المحتوى: {str(e)}"}
