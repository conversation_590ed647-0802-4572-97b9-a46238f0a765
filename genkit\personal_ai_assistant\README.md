# 🤖 المساعد الشخصي الذكي | Personal AI Assistant
## Advanced Multi-Skill AI Assistant with Latest Vertex AI Models

مساعد شخصي ذكي متطور مبني باستخدام **Google Agent Development Kit (ADK)** وأحدث نماذج **Vertex AI**. يدعم **Gemini 2.0 Flash** و **Gemini 2.5 Pro** مع جميع الأدوات المتقدمة مفعلة.

## 🆕 آخر التحديثات (ديسمبر 2024)

### 🔥 النماذج الجديدة المدعومة:
- ✅ **Gemini 2.0 Flash** - أحدث نموذج متعدد الوسائط (افتراضي)
- ✅ **Gemini 2.0 Flash-Lite** - محسن للكفاءة والسرعة
- 🔬 **Gemini 2.5 Pro** - أكثر نماذج التفكير تقدماً (معاينة)
- 🔬 **Gemini 2.5 Flash** - معاينة أحدث إصدار Flash (معاينة)

### 🛠️ التحسينات الجديدة:
- ✅ **تفعيل جميع الأدوات المتقدمة** (Excel, Web Search, File Operations)
- ✅ **نظام إدارة النماذج الذكي** مع التوصيات التلقائية
- ✅ **أداة اختبار النماذج** للتحقق من التوفر
- ✅ **مثبت التبعيات الآلي** في المجلد الرئيسي
- ✅ **تحسين معالجة الأخطاء** والاستقرار
- ✅ **دعم أفضل للغة العربية** في جميع الوظائف

---

## 📋 **جدول المحتويات**

1. [نظرة عامة](#نظرة-عامة)
2. [هيكل المشروع](#هيكل-المشروع)
3. [الميزات والوظائف](#الميزات-والوظائف)
4. [المتطلبات والتبعيات](#المتطلبات-والتبعيات)
5. [التثبيت والإعداد](#التثبيت-والإعداد)
6. [التشغيل](#التشغيل)
7. [أمثلة الاستخدام](#أمثلة-الاستخدام)
8. [شرح الكود المتقدم](#شرح-الكود-المتقدم)
9. [إدارة التبعيات](#إدارة-التبعيات)
10. [استكشاف الأخطاء](#استكشاف-الأخطاء)
11. [التطوير والتوسع](#التطوير-والتوسع)
12. [النشر والإنتاج](#النشر-والإنتاج)

---

## 🎯 **نظرة عامة**

هذا مساعد شخصي ذكي متكامل يوفر:
- **4 وظائف أساسية** قابلة للتوسع
- **دعم كامل للغة العربية والإنجليزية**
- **بنية قابلة للتطوير** مع نظام أدوات منظم
- **إدارة تبعيات متقدمة** مع جميع المكتبات المطلوبة
- **أدوات تشغيل وإدارة** مدمجة

**الهدف:** مساعد شخصي جاهز للاستخدام الفعلي مع إمكانية التطوير والتوسع.

---

## 📁 **هيكل المشروع**

```
personal_ai_assistant/
├── 📄 README.md                    # هذا الملف - دليل شامل ومفصل
├── 🤖 agent.py                     # الكود الرئيسي للمساعد (160+ سطر)
├── ⚙️ .env                         # إعدادات Google Cloud
├── 🐍 __init__.py                  # ملف Python package
├── 📦 requirements.txt             # قائمة التبعيات المنظمة
├── 🔧 install_dependencies.py     # تثبيت تلقائي للتبعيات
├── 🚀 run_assistant.py             # تشغيل المساعد مع فحوصات
├── 🗂️ tools/                       # مجلد الأدوات (قابل للتوسع)
│   └── 🐍 __init__.py              # ملف Python package للأدوات
└── 🗃️ __pycache__/                 # ملفات Python المترجمة
```

**إجمالي الكود:** ~400+ سطر مع إمكانيات متقدمة!

---

## ✨ **الميزات والوظائف المتقدمة**

### 🌤️ **1. نظام الطقس المتقدم**
- **المدن المدعومة:** القاهرة، الرياض، دبي، نيويورك، لندن
- **المعلومات:** درجة الحرارة، حالة الطقس، وصف مفصل
- **اللغات:** دعم كامل للعربية والإنجليزية
- **قابلية التوسع:** سهولة إضافة مدن جديدة

### 🕐 **2. نظام الوقت العالمي الذكي**
- **المناطق الزمنية:** 7 مناطق زمنية رئيسية
- **التنسيق:** عرض الوقت بتنسيق واضح مع المنطقة الزمنية
- **الدقة:** استخدام مكتبة `zoneinfo` للدقة العالية
- **المدن:** القاهرة، الرياض، دبي، نيويورك، لندن، طوكيو، باريس

### 🔍 **3. نظام البحث والمعرفة المحدث**
- **قاعدة معرفة:** معلومات تقنية ومفيدة محدثة
- **المواضيع:** Python، AI، Google Cloud، Vertex AI، Gemini 2.0
- **الذكاء:** إجابات ذكية ومفيدة مع السياق
- **قابلية التوسع:** سهولة إضافة معلومات جديدة

### 📝 **4. نظام إدارة المهام المتطور**
- **إضافة المهام:** إضافة مهام جديدة مع التواريخ
- **عرض المهام:** عرض قائمة المهام مع التنسيق
- **إدارة متقدمة:** تنظيم المهام حسب الأولوية
- **التطوير المستقبلي:** جاهز للربط بقاعدة بيانات

### 📊 **5. أدوات Excel المتقدمة (مفعلة بالكامل)**
- ✅ **قراءة ملفات Excel:** استخراج البيانات مع دعم أوراق متعددة
- ✅ **كتابة البيانات:** حفظ البيانات في ملفات Excel جديدة
- ✅ **تحليل البيانات:** إحصائيات أساسية وتحليل الهيكل
- ✅ **معالجة الأخطاء:** رسائل واضحة ومعالجة ذكية للأخطاء
- ✅ **دعم pandas:** استخدام مكتبة pandas للمعالجة المتقدمة

### 🌐 **6. أدوات البحث على الإنترنت (مفعلة بالكامل)**
- ✅ **بحث Google:** تنفيذ عمليات بحث حقيقية على Google
- ✅ **استخراج المحتوى:** جلب وتنظيف محتوى صفحات الويب
- ✅ **تصفية النتائج:** تحديد عدد النتائج مع تنسيق احترافي
- ✅ **معالجة الشبكة:** التعامل مع أخطاء الاتصال بذكاء
- ✅ **دعم requests:** استخدام مكتبة requests للاتصالات

### 📂 **7. إدارة الملفات والمجلدات (مفعلة بالكامل)**
- ✅ **قراءة الملفات:** عرض محتوى الملفات مع دعم UTF-8
- ✅ **كتابة الملفات:** إنشاء ملفات جديدة مع إنشاء المجلدات تلقائياً
- ✅ **استعراض المجلدات:** عرض محتويات مع تفاصيل الحجم والنوع
- ✅ **إنشاء المجلدات:** إنشاء مجلدات جديدة بأمان
- ✅ **دعم تنسيقات متعددة:** JSON, CSV, TXT وغيرها

## 🤖 **النماذج المدعومة والتوصيات**

### ✅ النماذج المتاحة:
| النموذج | الوصف | الاستخدام المُوصى به |
|---------|--------|---------------------|
| `gemini-2.0-flash` | أحدث نموذج متعدد الوسائط | المهام العامة (افتراضي) |
| `gemini-2.0-flash-lite` | محسن للكفاءة والسرعة | الاستجابة السريعة والكفاءة |

### 🔬 النماذج التجريبية (معاينة):
| النموذج | الوصف | الاستخدام المُوصى به |
|---------|--------|---------------------|
| `gemini-2.5-pro` | أكثر نماذج التفكير تقدماً | التفكير المعقد والتحليل |
| `gemini-2.5-flash` | معاينة أحدث إصدار Flash | تجربة الميزات الجديدة |

### 🎯 التوصيات حسب نوع المهمة:
- 🔄 **المهام العامة:** `gemini-2.0-flash`
- 🧠 **التفكير المعقد:** `gemini-2.5-pro`
- ⚡ **الاستجابة السريعة:** `gemini-2.0-flash-lite`
- 🆕 **الميزات الجديدة:** `gemini-2.5-flash`
- 💰 **الكفاءة في التكلفة:** `gemini-2.0-flash-lite`

---

## 🛠 **المتطلبات والتبعيات**

### **متطلبات النظام:**
- **Python:** 3.10 أو أحدث
- **نظام التشغيل:** Windows, macOS, أو Linux
- **الذاكرة:** 4GB RAM (موصى به 8GB)
- **مساحة القرص:** 2GB مساحة حرة
- **الإنترنت:** اتصال مستقر للوصول لـ Vertex AI

### **متطلبات Google Cloud:**
- حساب Google Cloud Platform نشط
- مشروع Google Cloud مع Vertex AI API مفعل
- إعداد الفوترة (يمكن استخدام الرصيد المجاني $300)
- Google Cloud CLI مثبت ومعد بشكل صحيح

### **التبعيات المحدثة:**

#### **🔧 التبعيات الأساسية:**
```bash
google-cloud-aiplatform[agent_engines,adk]  # Google Cloud AI Platform مع ADK
```

#### **📊 التبعيات المتقدمة (مفعلة):**
```bash
pandas>=2.0.0                        # معالجة البيانات وملفات Excel
openpyxl>=3.1.0                      # قراءة وكتابة ملفات Excel
requests>=2.31.0                     # طلبات HTTP للبحث على الإنترنت
beautifulsoup4>=4.12.0               # تحليل صفحات الويب
lxml>=4.9.0                          # محلل XML/HTML سريع
```

#### **🚀 التثبيت السريع:**
```bash
# استخدام مثبت التبعيات الآلي (الطريقة المُوصى بها)
python install_dependencies.py

# أو التثبيت اليدوي
pip install --upgrade --quiet google-cloud-aiplatform[agent_engines,adk]
pip install pandas openpyxl requests beautifulsoup4 lxml
## 🚀 **التثبيت والإعداد المحدث**

### **الطريقة السريعة (مُوصى بها):**

1. **استخدام مثبت التبعيات الآلي:**
```bash
# من المجلد الرئيسي genkit/
python install_dependencies.py
```

2. **إعداد متغيرات البيئة:**
```bash
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/credentials.json"
```

3. **تشغيل المساعد:**
```bash
cd personal_ai_assistant
adk web --port 8000
```

### **الطريقة اليدوية:**

1. **تثبيت التبعيات الأساسية:**
```bash
pip install --upgrade --quiet google-cloud-aiplatform[agent_engines,adk]
```

2. **تثبيت التبعيات المتقدمة:**
```bash
pip install pandas openpyxl requests beautifulsoup4 lxml
```

3. **اختبار النماذج المتاحة:**
```bash
# من المجلد الرئيسي
python test_models.py
```

## 🔧 **الاستخدام المحدث**

### **تشغيل الخادم:**
```bash
cd personal_ai_assistant
adk web --port 8000
```

### **الوصول للواجهة:**
افتح المتصفح وانتقل إلى: `http://localhost:8000`

### **اختبار النماذج:**
```bash
# اختبار النماذج المتاحة
python test_models.py

# عرض معلومات النماذج
python -c "from personal_ai_assistant.model_config import *; print('النماذج المتاحة:', get_available_models())"
```

## 📋 **الوظائف المتاحة المحدثة**

| الوظيفة | الوصف | المعاملات | الحالة |
|---------|--------|-----------|--------|
| `get_weather` | الحصول على معلومات الطقس | `city: str` | ✅ متاح |
| `get_current_time` | عرض الوقت الحالي | `city: str` | ✅ متاح |
| `search_information` | البحث في قاعدة المعرفة | `query: str` | ✅ محدث |
| `manage_tasks` | إدارة المهام | `action: str, task: str` | ✅ متاح |
| `read_excel_file` | قراءة ملف Excel | `file_path: str, sheet_name: str` | ✅ مفعل |
| `write_to_excel` | كتابة بيانات إلى Excel | `data: str, file_path: str` | ✅ مفعل |
| `search_web` | البحث على الإنترنت | `query: str, num_results: int` | ✅ مفعل |
| `read_file_content` | قراءة محتوى ملف | `file_path: str` | ✅ مفعل |
| `list_directory_contents` | عرض محتويات مجلد | `directory_path: str` | ✅ مفعل |

## 🛠️ **التخصيص والتطوير**

### **تغيير النموذج المستخدم:**
```python
# في ملف agent.py
root_agent = Agent(
    name="personal_ai_assistant",
    model="gemini-2.0-flash",  # أو أي نموذج آخر
    # ...
)
```

### **إضافة وظائف جديدة:**
1. أنشئ وظيفة جديدة في `agent.py`
2. أضف الوظيفة إلى قائمة `tools` في نهاية الملف
3. اختبر الوظيفة الجديدة

### **استخدام نماذج مختلفة:**
```python
# استخدام model_config للحصول على التوصيات
from model_config import get_recommended_model

# للمهام العامة
model = get_recommended_model("general")  # gemini-2.0-flash

# للتفكير المعقد
model = get_recommended_model("reasoning")  # gemini-2.5-pro

# للسرعة
model = get_recommended_model("fast")  # gemini-2.0-flash-lite
```

## 📁 **هيكل المشروع المحدث**

```
genkit/
├── 📄 README.md                           # التوثيق الرئيسي
├── 🔧 install_dependencies.py             # مثبت التبعيات الآلي
├── 📦 requirements.txt                    # قائمة التبعيات
├── 🧪 test_models.py                      # أداة اختبار النماذج
└── personal_ai_assistant/                 # المجلد الرئيسي للمساعد
    ├── 🤖 agent.py                        # المساعد الرئيسي (محدث)
    ├── 🔧 model_config.py                 # تكوين النماذج (جديد)
    ├── 📄 README.md                       # توثيق المساعد (محدث)
    ├── 🐍 __init__.py                     # ملف التصدير
    ├── 📦 requirements.txt                # نسخة احتياطية
    ├── 🔧 install_dependencies.py        # نسخة احتياطية
    └── 🗂️ tools/                          # مجلد الأدوات (محدث)
        ├── 🐍 __init__.py                 # تصدير الأدوات
        ├── 📊 excel_tools.py              # أدوات Excel (مفعلة)
        ├── 🌐 web_search.py               # أدوات البحث (مفعلة)
        ├── 📂 file_operations.py          # أدوات الملفات (مفعلة)
        ├── 📄 README.md                   # توثيق مجلد الأدوات
        └── 📚 TOOLS_DOCUMENTATION.md     # توثيق الأدوات المفصل
```

## 🔒 **الأمان والخصوصية**

### ✅ ضمانات الأمان:
- 🔒 **معالجة محلية** لجميع الملفات والبيانات
- 🔒 **عدم حفظ المحادثات** بشكل دائم
- 🔒 **طلبات آمنة** للإنترنت مع headers مناسبة
- 🔒 **معالجة أخطاء شاملة** لمنع التسريبات
- 🔒 **استخدام Google Cloud** للذكاء الاصطناعي فقط

### 🛠️ أفضل الممارسات:
- تحقق من مسارات الملفات قبل العمليات
- راجع النتائج قبل حفظ البيانات المهمة
- استخدم متغيرات البيئة للمعلومات الحساسة

## 🤝 **المساهمة والتطوير**

### 🆕 إضافة وظائف جديدة:
1. **إنشاء الوظيفة** في `agent.py`
2. **إضافة التوثيق** مع أمثلة
3. **اختبار الوظيفة** مع حالات مختلفة
4. **إضافتها لقائمة tools** في نهاية الملف

### 🔧 تطوير الأدوات:
1. **إنشاء ملف جديد** في مجلد `tools/`
2. **اتباع نفس النمط** المستخدم في الأدوات الموجودة
3. **إضافة الأداة** إلى `__init__.py`
4. **دمجها في المساعد** الرئيسي

### 🧪 الاختبار:
```bash
# اختبار النماذج
python test_models.py

# اختبار التبعيات
python install_dependencies.py

# اختبار المساعد
cd personal_ai_assistant
adk web --port 8000
```

## 📄 **الترخيص**

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🆘 **الدعم والمساعدة**

### 📚 الموارد:
- [توثيق Google ADK](https://google.github.io/adk-docs/)
- [توثيق Vertex AI](https://cloud.google.com/vertex-ai/docs)
- [نماذج Google Vertex AI](https://cloud.google.com/vertex-ai/generative-ai/docs/models)
- [مجتمع Google Cloud](https://www.googlecloudcommunity.com/)

### 🔧 استكشاف الأخطاء:
1. **تحقق من التبعيات:** `python install_dependencies.py`
2. **اختبر النماذج:** `python test_models.py`
3. **تحقق من Google Cloud:** متغيرات البيئة والمشروع
4. **راجع السجلات:** في terminal عند تشغيل ADK

### 💬 الحصول على المساعدة:
- راجع التوثيق المفصل في هذا الملف
- تحقق من ملفات الأدوات في `tools/TOOLS_DOCUMENTATION.md`
- استخدم أداة اختبار النماذج للتشخيص

---

## 🎉 **الخلاصة**

**المساعد الشخصي الذكي** هو مشروع متكامل ومتطور يستخدم أحدث تقنيات Google Vertex AI لتوفير تجربة ذكية ومفيدة. مع دعم **Gemini 2.0 Flash** و **Gemini 2.5 Pro** وجميع الأدوات المتقدمة مفعلة، يمكنك الاستفادة من:

- 🤖 **ذكاء اصطناعي متقدم** مع أحدث النماذج
- 📊 **معالجة ملفات Excel** بشكل احترافي
- 🌐 **البحث على الإنترنت** مع نتائج حقيقية
- 📂 **إدارة الملفات** بطريقة ذكية وآمنة
- 🌤️ **معلومات الطقس والوقت** في أي مكان
- 📝 **إدارة المهام** بكفاءة

**ابدأ رحلتك مع المساعد الذكي اليوم!** 🚀

---

**تم تطوير هذا المشروع باستخدام أحدث تقنيات الذكاء الاصطناعي من Google** 🌟
