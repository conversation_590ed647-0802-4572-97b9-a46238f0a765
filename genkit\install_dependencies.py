#!/usr/bin/env python3
"""
تثبيت جميع التبعيات المطلوبة للمساعد الشخصي الذكي
Install all required dependencies for Personal AI Assistant
"""

import subprocess
import sys

def install_package(package):
    """تثبيت حزمة Python"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ تم تثبيت {package} بنجاح")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ فشل في تثبيت {package}")
        return False

def main():
    """تثبيت جميع التبعيات"""
    print("🚀 بدء تثبيت التبعيات...")
    
    # قائمة التبعيات الأساسية
    dependencies = [
        "google-adk",
        "requests", 
        "beautifulsoup4",
        "pandas",
        "numpy",
        "python-dotenv",
        "schedule",
        "pytz",
        "lxml"
    ]
    
    success_count = 0
    total_count = len(dependencies)
    
    for package in dependencies:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 النتائج:")
    print(f"✅ تم تثبيت {success_count} من {total_count} حزمة بنجاح")
    
    if success_count == total_count:
        print("🎉 تم تثبيت جميع التبعيات بنجاح!")
        print("يمكنك الآن تشغيل المساعد الشخصي باستخدام: adk web")
    else:
        print("⚠️ بعض الحزم لم يتم تثبيتها. يرجى المحاولة مرة أخرى.")

if __name__ == "__main__":
    main()
